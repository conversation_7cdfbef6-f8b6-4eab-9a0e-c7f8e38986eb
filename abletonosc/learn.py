from functools import partial
import time
from typing import Op<PERSON>, Tu<PERSON>, Any
from .handler import <PERSON>bleton<PERSON><PERSON><PERSON><PERSON>
from .utils.parameter_throttler import ParameterThrottler

# Import des handlers d'encoders pour le mode learn
from .learn_mode_helper.learn_mode_encoder_handlers import (
    adjust_learn_parameter,
    _adjust_track_volume,
    _adjust_track_pan,
    _adjust_track_send,
    _adjust_device_parameter,
    _adjust_chain_volume,
    _adjust_chain_pan,
    _adjust_track_mute,
    _adjust_track_solo,
    _adjust_chain_mute,
    _adjust_chain_solo,
    _adjust_quantized_parameter,
    _is_device_rack,
    _is_parameter_quantized
)

class LearnModeHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "learn"
        self.learn_slots = {i: {
            "track": None,
            "device": None,
            "param_type": None,
            "param_index": None
        } for i in range(32)}
        self.learn_listeners = {}
        self.parameter_throttler = ParameterThrottler()

        # Buffers pour les paramètres quantifiés (similaire au mode device)
        self.quantized_param_buffers = {}

    # Intégration des méthodes importées pour les handlers d'encoders
    adjust_learn_parameter = adjust_learn_parameter
    _adjust_track_volume = _adjust_track_volume
    _adjust_track_pan = _adjust_track_pan
    _adjust_track_send = _adjust_track_send
    _adjust_device_parameter = _adjust_device_parameter
    _adjust_chain_volume = _adjust_chain_volume
    _adjust_chain_pan = _adjust_chain_pan
    _adjust_track_mute = _adjust_track_mute
    _adjust_track_solo = _adjust_track_solo
    _adjust_chain_mute = _adjust_chain_mute
    _adjust_chain_solo = _adjust_chain_solo
    _adjust_quantized_parameter = _adjust_quantized_parameter
    _is_device_rack = _is_device_rack
    _is_parameter_quantized = _is_parameter_quantized

    def init_api(self):

     # Enregistrement des handlers pour le learn mode
        self.osc_server.add_handler("/live/learn/set/value", self.set_learn_slot_value)
        self.osc_server.add_handler("/live/learn/stop", lambda _: self.stop_learn_listen())
        self.osc_server.add_handler("/live/learn/slot", self.start_learn_listen)
        self.osc_server.add_handler("/live/learn/select", self.select_learn_slot_element)
        self.osc_server.add_handler("/live/learn/del_slot", self.delete_single_slot)
        self.osc_server.add_handler("/live/learn/set", self.set_learn_slot_value)

        # Handler pour les encoders du mode learn
        self.osc_server.add_handler("/live/learn/adjust/parameter",
            lambda params: self.adjust_learn_parameter(params))

    def _setup_track_listeners(self, learn_slot: int, track_name: str, track_to_monitor) -> None:
        """Configure les listeners de base pour une piste (nom et couleur)"""
        # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
        if not hasattr(self, '_last_reference_send_time'):
            self._last_reference_send_time = {}

        # Fonction pour éviter les envois multiples trop rapprochés
        def debounced_send_reference():
            current_time = time.time()
            last_time = self._last_reference_send_time.get(learn_slot, 0)

            # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
            if current_time - last_time < 0.1:
                self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
                return

            self._last_reference_send_time[learn_slot] = current_time
            self.send_learn_slot_reference(learn_slot)

        def track_color_changed_callback():
            debounced_send_reference()

        def track_name_changed_callback():
            debounced_send_reference()

        track_to_monitor.add_color_listener(track_color_changed_callback)
        track_to_monitor.add_name_listener(track_name_changed_callback)

        self.learn_listeners[f"learn_{track_name}_color"] = track_color_changed_callback
        self.learn_listeners[f"learn_{track_name}_name"] = track_name_changed_callback

    def _setup_volume_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
        """Configure le listener pour le volume"""
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                value = learned_track.mixer_device.volume.value
                param_id = f"learn_{track_name}_volume"

                def send_value(val):
                    self.logger.info(f"Volume changed for {track_name}: {val}")
                    self.osc_server.send("/live/tracklearn/get/volume", (learn_slot, val))

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        listener_key = (track_name, "volume")
        learned_track.mixer_device.volume.add_value_listener(parameter_changed_callback)
        self.learn_listeners[listener_key] = parameter_changed_callback
        parameter_changed_callback()

    def _setup_pan_listener(self, learn_slot: int, track_name: str, learned_track) -> None:
        """Configure le listener pour le panoramique"""
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                value = learned_track.mixer_device.panning.value
                param_id = f"learn_{track_name}_panning"

                def send_value(val):
                    self.logger.info(f"Panning changed for {track_name}: {val}")
                    self.osc_server.send("/live/tracklearn/get/panning", (learn_slot, val))

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        listener_key = (track_name, "panning")
        learned_track.mixer_device.panning.add_value_listener(parameter_changed_callback)
        self.learn_listeners[listener_key] = parameter_changed_callback
        parameter_changed_callback()

    def _setup_send_listener(self, learn_slot: int, track_name: str, learned_track, send_index: int) -> None:
        """Configure le listener pour un send"""
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                value = learned_track.mixer_device.sends[send_index].value
                param_id = f"learn_{track_name}_send_{send_index}"

                def send_value(val):
                    self.logger.info(f"Send {send_index} changed for {track_name}: {val}")
                    self.osc_server.send("/live/tracklearn/get/sends", (learn_slot, send_index, val))

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        listener_key = (track_name, f"send_{send_index}")
        learned_track.mixer_device.sends[send_index].add_value_listener(parameter_changed_callback)
        self.learn_listeners[listener_key] = parameter_changed_callback
        parameter_changed_callback()

    def _setup_device_parameter_listener(self, learn_slot: int, track_name: str, learned_device, param_index: int) -> None:
        """Configure le listener pour un paramètre de device"""
        parameter_name = f"device_param_{param_index}"
        parameter_object = learned_device.parameters[param_index]

        def create_parameter_callback(slot_number, device, param_idx):
            def parameter_changed_callback():
                if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                    try:
                        value = device.parameters[param_idx].value
                        value_string = device.parameters[param_idx].str_for_value(value)
                        param_id = f"learn_{track_name}_{parameter_name}"

                        def send_value(val):
                            self.logger.info(f"Parameter {param_idx} changed for device in slot {slot_number}: {val} ({value_string})")
                            self.osc_server.send("/live/devicelearn/get/parameter/value",
                                        (slot_number, param_idx, val, value_string))

                        self.parameter_throttler.update_parameter(param_id, value, send_value)

                    except Exception as e:
                        self.logger.error(f"Erreur dans parameter_changed_callback: {e}")
            return parameter_changed_callback

        parameter_changed_callback = create_parameter_callback(learn_slot, learned_device, param_index)

        try:
            parameter_object.add_value_listener(parameter_changed_callback)
            self.learn_listeners[f"learn_{track_name}_{parameter_name}"] = parameter_changed_callback
            self.logger.info(f"Listener ajouté pour {parameter_name}")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout du listener: {e}")

    def _setup_device_listeners(self, learn_slot: int, track_name: str, learned_device, learned_track) -> None:
        """Configure les listeners supplémentaires pour un device (nom, nom de piste, couleur)"""
        # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
        if not hasattr(self, '_last_reference_send_time'):
            self._last_reference_send_time = {}

        # Fonction pour éviter les envois multiples trop rapprochés
        def debounced_send_reference():
            current_time = time.time()
            last_time = self._last_reference_send_time.get(learn_slot, 0)

            # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
            if current_time - last_time < 0.1:
                self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
                return

            self._last_reference_send_time[learn_slot] = current_time
            self.send_learn_slot_reference(learn_slot)

        def device_name_callback():
            debounced_send_reference()

        def track_name_callback():
            debounced_send_reference()

        def track_color_callback():
            debounced_send_reference()

        # Stocker les callbacks pour pouvoir les réutiliser
        callback_key = f"learn_{track_name}_device_callbacks"

        # Vérifier si des callbacks existent déjà pour ce slot
        if callback_key in self.learn_listeners:
            self.logger.warning(f"Des callbacks device existent déjà pour le slot {learn_slot}, ils seront remplacés")
            # On pourrait ajouter ici du code pour supprimer les anciens listeners si nécessaire

        self.learn_listeners[callback_key] = {
            "device_name": device_name_callback,
            "track_name": track_name_callback,
            "track_color": track_color_callback
        }

        # Ajouter les listeners initiaux
        learned_device.add_name_listener(device_name_callback)
        learned_track.add_name_listener(track_name_callback)
        learned_track.add_color_listener(track_color_callback)

        self.logger.debug(f"Device context listeners ajoutés pour le slot {learn_slot} (device: {learned_device.name}, track: {learned_track.name})")

    def _update_device_track_listeners(self, learn_slot: int, track_name: str, device_param, new_track) -> None:
        """Met à jour les listeners de piste quand un device ou une chaîne change de piste"""
        old_track = self.learn_slots[learn_slot]["track"]
        param_type = self.learn_slots[learn_slot]["param_type"]

        if param_type == 4:
            callbacks = self.learn_listeners.get(f"learn_{track_name}_device_callbacks", {})
        elif param_type in [5, 6, 9, 10]:  # Traiter tous les paramètres de chaîne de la même façon
            callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})

        # Supprimer les anciens listeners
        if old_track and callbacks:
            old_track.remove_name_listener(callbacks["track_name"])
            old_track.remove_color_listener(callbacks["track_color"])

        # Ajouter les listeners à la nouvelle piste
        new_track.add_name_listener(callbacks["track_name"])
        new_track.add_color_listener(callbacks["track_color"])

        # Mettre à jour la référence de la piste dans le slot
        self.learn_slots[learn_slot]["track"] = new_track

        self.send_learn_slot_reference(learn_slot)

    def _check_duplicates(self, learn_slot: int, param_type: int, track, chain=None, device=None, param_index=None, send_index=None) -> None:
        """Vérifie et supprime les doublons dans les slots d'apprentissage"""
        self.logger.debug(f"Checking duplicates for slot {learn_slot}, type {param_type}")
        self.logger.debug(f"Input params - Track: {track.name if track else None}, Device: {device.name if device else None}, Chain: {chain.name if chain else None}")
        self.logger.debug(f"Param index: {param_index}, Send index: {send_index}")

        for slot_index, slot_data in self.learn_slots.items():
            # Ne pas vérifier le slot actuel
            if slot_index == learn_slot:
                continue

            slot_param_type = slot_data.get("param_type")
            slot_device = slot_data.get("device")
            slot_chain = slot_data.get("chain")
            slot_track = slot_data.get("track")

            self.logger.debug(f"Checking slot {slot_index}:")
            self.logger.debug(f"- Type: {slot_param_type}")
            self.logger.debug(f"- Device: {slot_device.name if slot_device else None}")
            self.logger.debug(f"- Chain: {slot_chain.name if slot_chain else None}")
            self.logger.debug(f"- Track: {slot_track.name if slot_track else None}")

            # Si le slot est vide ou les types ne correspondent pas
            if slot_param_type is None or slot_param_type != param_type:
                continue

            # Types 5, 6, 9 et 10: Chain Volume, Chain Pan, Chain Mute, Chain Solo
            if param_type in [5, 6, 9, 10] and chain and slot_chain:
                try:
                    # Vérifier si c'est exactement la même chaîne
                    if chain == slot_chain:
                        self.logger.info(f"Found duplicate chain parameter in slot {slot_index}")
                        self.delete_single_slot(slot_index)
                except Exception as e:
                    self.logger.error(f"Error checking chain duplicate: {e}")

            # Type 4: Device Parameter
            elif param_type == 4 and device and slot_device:
                try:
                    # Vérifier si c'est exactement le même device et le même paramètre
                    if (device == slot_device and
                        param_index == slot_data.get("param_index")):
                        self.logger.info(f"Found duplicate device parameter in slot {slot_index}")
                        self.delete_single_slot(slot_index)
                except Exception as e:
                    self.logger.error(f"Error checking device duplicate: {e}")

            # Types 1, 2, 3, 7, 8: Track-based parameters (Volume, Pan, Send, Mute, Solo)
            elif track and slot_track and track == slot_track:
                if param_type in [1, 2, 7, 8]:  # Volume, Pan, Mute, Solo
                    self.logger.info(f"Found duplicate track parameter in slot {slot_index}")
                    self.delete_single_slot(slot_index)
                elif param_type == 3 and send_index == slot_data.get("send_index"):  # Send
                    self.logger.info(f"Found duplicate send parameter in slot {slot_index}")
                    self.delete_single_slot(slot_index)

    def _setup_chain_volume_listener(self, learn_slot: int, track_name: str, chain) -> None:
        """Configure le listener pour le volume de la chaîne"""
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                value = chain.mixer_device.volume.value
                chain_path = self._get_chain_path(chain)
                param_id = f"learn_{track_name}_chain_volume"

                def send_value(val):
                    self.logger.info(f"Chain volume changed for {track_name}: {val}, chain_path: {chain_path}")
                    # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
                    self.osc_server.send("/live/chainlearn/get/volume", (learn_slot, val))

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        listener_key = (track_name, "chain_volume")
        chain.mixer_device.volume.add_value_listener(parameter_changed_callback)
        self.learn_listeners[listener_key] = parameter_changed_callback
        parameter_changed_callback()

    def _setup_chain_pan_listener(self, learn_slot: int, track_name: str, chain) -> None:
        """Configure le listener pour le panoramique de la chaîne"""
        def parameter_changed_callback():
            if not hasattr(parameter_changed_callback, 'is_active') or parameter_changed_callback.is_active:
                value = chain.mixer_device.panning.value
                chain_path = self._get_chain_path(chain)
                param_id = f"learn_{track_name}_chain_panning"

                def send_value(val):
                    self.logger.info(f"Chain panning changed for {track_name}: {val}, chain_path: {chain_path}")
                    # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
                    self.osc_server.send("/live/chainlearn/get/panning", (learn_slot, val))

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        listener_key = (track_name, "chain_panning")
        chain.mixer_device.panning.add_value_listener(parameter_changed_callback)
        self.learn_listeners[listener_key] = parameter_changed_callback
        parameter_changed_callback()

    def _get_chain_path(self, chain):
        """
        Récupère le chemin complet d'une chaîne sous forme de liste d'indices.
        Similaire à _get_chain_path_indices dans device_mode_environment.py
        """
        try:
            self.logger.debug(f"Récupération du chemin pour la chaîne: {chain.name}")

            def find_chain_path(devices, target_chain):
                for rack_index, device in enumerate(devices):
                    if hasattr(device, 'chains'):
                        # Vérifier si la chaîne cible est directement dans ce rack
                        for chain_index, current_chain in enumerate(device.chains):
                            if current_chain == target_chain:
                                return [rack_index, chain_index]

                            # Chercher dans les devices de cette chaîne
                            if current_chain.devices:
                                deeper_path = find_chain_path(current_chain.devices, target_chain)
                                if deeper_path:
                                    return [rack_index, chain_index] + deeper_path
                return None

            # Commencer la recherche depuis les devices de la piste
            selected_track = self.song.view.selected_track
            if not selected_track:
                return []

            path = find_chain_path(selected_track.devices, chain) or []

            self.logger.debug(f"Chemin trouvé pour la chaîne: {path}")
            return path

        except Exception as e:
            self.logger.error(f"Erreur dans _get_chain_path: {str(e)}")
            return []

    def _get_chain_by_path(self, chain_path):
        """
        Récupère une chaîne à partir de son chemin.
        Le chemin est une liste d'indices [rack_idx, chain_idx, ...]
        """
        try:
            if not chain_path or len(chain_path) < 2:
                self.logger.warning("Chemin de chaîne invalide ou trop court")
                return None

            # Convertir tous les indices en entiers si ce n'est pas déjà le cas
            if not all(isinstance(idx, int) for idx in chain_path):
                try:
                    chain_path = [int(idx) for idx in chain_path]
                except (ValueError, TypeError) as e:
                    self.logger.error(f"Erreur de conversion des indices: {e}")
                    return None

            selected_track = self.song.view.selected_track
            if not selected_track:
                self.logger.warning("Aucune piste sélectionnée")
                return None

            current_container = selected_track
            current_path = []

            # Parcourir le chemin
            for i in range(0, len(chain_path), 2):
                if i + 1 >= len(chain_path):
                    self.logger.warning(f"Chemin incomplet, besoin d'au moins un indice rack et un indice chaîne: {chain_path}")
                    return None

                rack_idx = chain_path[i]
                chain_idx = chain_path[i + 1]

                # Vérifier si nous avons accès aux devices
                if not hasattr(current_container, 'devices'):
                    self.logger.warning(f"Le conteneur n'a pas de devices à la position {current_path}")
                    return None

                # Vérifier si l'index du rack est valide
                if rack_idx < 0 or rack_idx >= len(current_container.devices):
                    self.logger.warning(f"Index de rack invalide: {rack_idx}")
                    return None

                rack = current_container.devices[rack_idx]
                current_path.append(rack_idx)

                # Vérifier si le device est un rack
                if not hasattr(rack, 'chains'):
                    self.logger.warning(f"Le device n'est pas un rack à la position {current_path}")
                    return None

                # Vérifier si l'index de la chaîne est valide
                if chain_idx < 0 or chain_idx >= len(rack.chains):
                    self.logger.warning(f"Index de chaîne invalide: {chain_idx}")
                    return None

                chain = rack.chains[chain_idx]
                current_path.append(chain_idx)

                # Si nous avons plus de chemin à parcourir, la chaîne devient le nouveau conteneur
                if i + 2 < len(chain_path):
                    current_container = chain
                else:
                    return chain

            return None

        except Exception as e:
            self.logger.error(f"Erreur dans _get_chain_by_path: {str(e)}")
            return None

    def _setup_chain_listeners(self, learn_slot: int, track_name: str, chain, track) -> None:
        """Configure les listeners pour une chaîne (nom de la chaîne, nom de piste, couleur)"""
        # Utiliser un dictionnaire pour stocker le dernier timestamp d'envoi pour chaque slot
        if not hasattr(self, '_last_reference_send_time'):
            self._last_reference_send_time = {}

        # Fonction pour éviter les envois multiples trop rapprochés
        def debounced_send_reference():
            current_time = time.time()
            last_time = self._last_reference_send_time.get(learn_slot, 0)

            # Si le dernier envoi était il y a moins de 100ms, ne pas envoyer à nouveau
            if current_time - last_time < 0.1:
                self.logger.debug(f"Ignoring duplicate reference send for slot {learn_slot} (debounce)")
                return

            self._last_reference_send_time[learn_slot] = current_time
            self.send_learn_slot_reference(learn_slot)

        def chain_name_callback():
            debounced_send_reference()

        def track_name_callback():
            debounced_send_reference()

        def track_color_callback():
            debounced_send_reference()

        def chains_changed_callback():
            self.logger.info(f"Chains changed in rack for slot {learn_slot}")
            self.verify_learn_slots_validity()

        # Stocker les callbacks
        callback_key = f"learn_{track_name}_chain_callbacks"

        # Vérifier si des callbacks existent déjà pour ce slot
        if callback_key in self.learn_listeners:
            self.logger.warning(f"Des callbacks chain existent déjà pour le slot {learn_slot}, ils seront remplacés")
            # On pourrait ajouter ici du code pour supprimer les anciens listeners si nécessaire

        self.learn_listeners[callback_key] = {
            "chain_name": chain_name_callback,
            "track_name": track_name_callback,
            "track_color": track_color_callback,
            "chains_changed": chains_changed_callback
        }

        # Ajouter les listeners
        chain.add_name_listener(chain_name_callback)
        track.add_name_listener(track_name_callback)
        track.add_color_listener(track_color_callback)
        # Ajouter le listener sur les chaînes du rack parent
        chain.canonical_parent.add_chains_listener(chains_changed_callback)

        self.logger.debug(f"Chain context listeners ajoutés pour le slot {learn_slot} (chain: {chain.name}, track: {track.name})")


    def start_learn_listen(self, params):
        """Démarre l'écoute des paramètres pour un slot d'apprentissage"""
        self.logger.debug(f"start_learn_listen appelé avec params: {params}")
        learn_slot = int(params[0])
        param_type = int(params[1])
        track_index = int(params[2])
        learned_track = None
        learned_device = None
        learned_chain = None
        chain_path = None
        param_index = None

        try:
            # Gestion spéciale pour track_index = -1 (locked track)
            if track_index == -1 and self.manager.trackLock and self.manager.lockedTrack:
                learned_track = self.manager.lockedTrack
            else:
                # Comportement existant pour les autres cas
                all_tracks = self.manager.get_visible_tracks()
                if 0 <= track_index < len(all_tracks):
                    learned_track = all_tracks[track_index]

            # Configuration spéciale pour le volume et le pan de chaîne (type 5 et 6)
            if param_type in [5, 6, 9, 10]:  # Ajout des types 9 et 10 ici
                if isinstance(params[3], list):  # Si on reçoit un chemin sous forme de liste
                    chain_path = params[3]
                    self.logger.debug(f"Chain path reçu sous forme de liste: {chain_path}")
                    selected_track = self.song.view.selected_track

                    if not selected_track:
                        self.logger.warning("Aucune piste sélectionnée")
                        return

                    learned_chain = self._get_chain_by_path(chain_path)

                    if not learned_chain:
                        self.logger.warning(f"Impossible de trouver la chaîne avec le chemin: {chain_path}")
                        return

                    # Important: s'assurer que learned_track est défini sur la piste contenant la chaîne
                    learned_track = selected_track
                elif isinstance(params[3], str) and params[3].startswith("[") and params[3].endswith("]"):  # JSON string
                    # Essayer de parser le JSON
                    import json
                    try:
                        chain_path = json.loads(params[3])
                        self.logger.debug(f"Chain path reçu sous forme de JSON: {chain_path}")
                        selected_track = self.song.view.selected_track

                        if not selected_track:
                            self.logger.warning("Aucune piste sélectionnée")
                            return

                        learned_chain = self._get_chain_by_path(chain_path)

                        if not learned_chain:
                            self.logger.warning(f"Impossible de trouver la chaîne avec le chemin JSON: {chain_path}")
                            return

                        # Important: s'assurer que learned_track est défini sur la piste contenant la chaîne
                        learned_track = selected_track
                    except json.JSONDecodeError as e:
                        self.logger.error(f"Erreur lors du décodage JSON du chemin de chaîne: {e}")
                        return
                else:  # Ancien format (chaîne de caractères)
                    chain_id = params[3]  # Le chain_id est maintenant fourni directement (format "rack_idx_chain_idx" ou "-1")
                    self.logger.debug(f"Chain ID reçu sous forme de chaîne: {chain_id}")
                    selected_track = self.song.view.selected_track
                    if not selected_track:
                        self.logger.warning("Aucune piste sélectionnée")
                        return

                    # Cas spécial pour chain_id = "-1" : utiliser la selected_chain
                    if chain_id == "-1":
                        selected_device = selected_track.view.selected_device
                        if not selected_device or not hasattr(selected_device, 'chains'):
                            self.logger.warning("Pas de rack sélectionné")
                            return

                        selected_chain = selected_device.view.selected_chain
                        if not selected_chain:
                            self.logger.warning("Pas de chaîne sélectionnée")
                            return

                        learned_chain = selected_chain
                        # Récupérer le vrai chemin de chaîne pour le stockage
                        chain_path = self._get_chain_path(selected_chain)
                    else:
                        # Essayer de convertir en chemin
                        try:
                            rack_idx, chain_idx = map(int, chain_id.split('_'))
                            chain_path = [rack_idx, chain_idx]
                            learned_chain = self._get_chain_by_path(chain_path)
                        except Exception as e:
                            self.logger.error(f"Erreur lors de la conversion du chain_id en chemin: {e}")
                            return

                    # Important: s'assurer que learned_track est défini sur la piste contenant la chaîne
                    learned_track = selected_track

            # Configuration spéciale pour les paramètres de device (type 4)
            elif param_type == 4:
                device_index = int(params[3])
                param_index = int(params[4])

                # Utiliser le lockedDevice si device_index est -4
                if device_index == -4 and self.manager.deviceLock and self.manager.lockedDevice:
                    learned_device = self.manager.lockedDevice
                    learned_track = self._find_device_or_chain_track(learned_device)
                else:
                    selected_track = self.song.view.selected_track
                    visible_devices = self.manager.get_visible_devices(selected_track)
                    if 0 <= device_index < len(visible_devices):
                        learned_device = visible_devices[device_index]
                        learned_track = selected_track

                if learned_device:
                    self.learn_slots[learn_slot]["param_index"] = param_index
                else:
                    raise Exception("Device not found")

        except Exception as e:
            self.logger.error(f"Error getting track/device: {e}")
            return

        # Vérifier que learned_track est défini
        if not learned_track:
            self.logger.error("Impossible de déterminer la piste associée")
            return

        # Vérification des doublons
        send_index = int(params[3]) if param_type == 3 else None
        self._check_duplicates(learn_slot, param_type, learned_track,
                             chain=learned_chain,
                             device=learned_device,
                             param_index=param_index,
                             send_index=send_index)

        # Configuration du slot
        track_name = f"track{learn_slot}"
        self.learn_slots[learn_slot]["track"] = learned_track
        self.learn_slots[learn_slot]["param_type"] = param_type

        # Configuration des listeners selon le type de paramètre
        if param_type == 1:
            self._setup_volume_listener(learn_slot, track_name, learned_track)
        elif param_type == 2:
            self._setup_pan_listener(learn_slot, track_name, learned_track)
        elif param_type == 3:
            self._setup_send_listener(learn_slot, track_name, learned_track, send_index)
            self.learn_slots[learn_slot]["send_index"] = send_index
        elif param_type == 4 and learned_device:
            self.learn_slots[learn_slot]["device"] = learned_device
            self._setup_device_listeners(learn_slot, track_name, learned_device, learned_track)
            if param_index < len(learned_device.parameters):
                self._setup_device_parameter_listener(learn_slot, track_name, learned_device, param_index)
        elif param_type == 5 and learned_chain:
            self.learn_slots[learn_slot]["chain"] = learned_chain
            self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
            self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
            self._setup_chain_volume_listener(learn_slot, track_name, learned_chain)
        elif param_type == 6 and learned_chain:
            self.learn_slots[learn_slot]["chain"] = learned_chain
            self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
            self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
            self._setup_chain_pan_listener(learn_slot, track_name, learned_chain)
        elif param_type == 7 and learned_track and learned_track != self.song.master_track:  # Mute
            self._setup_mute_listener(learn_slot, track_name, learned_track)
        elif param_type == 8 and learned_track and learned_track != self.song.master_track:  # Solo
            self._setup_solo_listener(learn_slot, track_name, learned_track)
        elif param_type == 9 and learned_chain:  # Chain Mute
            self.learn_slots[learn_slot]["chain"] = learned_chain
            self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
            self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
            self._setup_chain_mute_listener(learn_slot, track_name, learned_chain)
        elif param_type == 10 and learned_chain:  # Chain Solo
            self.learn_slots[learn_slot]["chain"] = learned_chain
            self.learn_slots[learn_slot]["chain_path"] = chain_path  # Stocker le chemin de chaîne
            self._setup_chain_listeners(learn_slot, track_name, learned_chain, learned_track)
            self._setup_chain_solo_listener(learn_slot, track_name, learned_chain)

        # Vérifier que track_to_monitor n'est pas None avant de configurer les listeners
        if learned_track:
            # Configuration des listeners de base pour la piste
            track_to_monitor = learned_track
            self._setup_track_listeners(learn_slot, track_name, track_to_monitor)

            # Envoi des références initiales
            self.send_learn_slot_reference(learn_slot)

            # Log du nombre total de listeners après configuration
            self.logger.info(f"Slot {learn_slot} configuré. Nombre total de listeners: {len(self.learn_listeners)}")
        else:
            self.logger.error(f"Impossible de configurer les listeners pour le slot {learn_slot} : piste non valide")

    def select_learn_slot_element(self, params):
        learn_slot = int(params[0])
        slot_info = self.learn_slots.get(learn_slot, {})

        track = slot_info.get("track")
        device = slot_info.get("device")

        if track:
            # Sélectionner la piste
            self.song.view.selected_track = track
        if device:
            # Sélectionner le device
            self.song.view.select_device(device)

    def set_learn_slot_value(self, params):
        # Le handler OSC /live/set prend maintenant 2 arguments: slot et value
        if len(params) < 2:
            self.logger.error(f"Nombre de paramètres insuffisant pour /live/set/learn: {params}")
            return

        try:
            slot = int(params[0])
            value = float(params[1])
        except (ValueError, TypeError) as e:
            self.logger.error(f"Erreur de conversion des paramètres OSC: {e}")
            return

        self.logger.debug(f"Traitement /live/set: slot={slot}, value={value}")

        if slot not in self.learn_slots:
            self.logger.error(f"Index de slot d'apprentissage invalide: {slot}")
            return

        slot_data = self.learn_slots[slot]
        track = slot_data.get("track")
        device = slot_data.get("device")
        chain = slot_data.get("chain")
        param_type = slot_data.get("param_type")

        if param_type == 1:  # Volume Track
            if track:
                track.mixer_device.volume.value = value
                self.logger.debug(f"Volume piste (slot {slot}) réglé sur {value}")
            else:
                self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 2:  # Pan Track
            if track:
                track.mixer_device.panning.value = value
                self.logger.debug(f"Panoramique piste (slot {slot}) réglé sur {value}")
            else:
                self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 3:  # Send Track
            send_index = slot_data.get("send_index")
            if track and send_index is not None and 0 <= send_index < len(track.mixer_device.sends):
                track.mixer_device.sends[send_index].value = value
                self.logger.debug(f"Send {send_index} piste (slot {slot}) réglé sur {value}")
            else:
                if not track:
                    self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
                elif send_index is None:
                    self.logger.warning(f"Index de Send non défini pour le slot {slot}")
                elif not (0 <= send_index < len(track.mixer_device.sends)):
                    self.logger.warning(f"Index de Send invalide ({send_index}) pour la piste du slot {slot}")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 4:  # Device Parameter
            param_index = slot_data.get("param_index")
            if device and param_index is not None and 0 <= param_index < len(device.parameters):
                device.parameters[param_index].value = value
                self.logger.debug(f"Paramètre device {param_index} (slot {slot}) réglé sur {value}")
            else:
                if not device:
                    self.logger.warning(f"Aucun device trouvé pour le slot {slot} (type {param_type})")
                elif param_index is None:
                    self.logger.warning(f"Index de paramètre non défini pour le slot {slot}")
                elif not (0 <= param_index < len(device.parameters)):
                    self.logger.warning(f"Index de paramètre invalide ({param_index}) pour le device du slot {slot}")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 5:  # Chain Volume
            if chain:
                chain.mixer_device.volume.value = value
                chain_path = self._get_chain_path(chain)
                self.osc_server.send("/live/chain/learning/volume", (-2, chain_path, value))
                self.logger.debug(f"Volume chaîne (slot {slot}) réglé sur {value}")
            else:
                self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 6:  # Chain Pan
            if chain:
                chain.mixer_device.panning.value = value
                chain_path = self._get_chain_path(chain)
                self.osc_server.send("/live/chain/learning/panning", (-2, chain_path, value))
                self.logger.debug(f"Panoramique chaîne (slot {slot}) réglé sur {value}")
            else:
                self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 7:  # Mute Track
            if track and track != self.song.master_track:
                track.mute = not track.mute  # Toggle du mute
                new_mute_state = 1 if track.mute else 0
                self.logger.debug(f"Mute piste (slot {slot}) basculé à {bool(new_mute_state)}")
            else:
                if not track:
                    self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
                elif track == self.song.master_track:
                    self.logger.warning(f"Impossible de muter la piste Master via le slot {slot}")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 8:  # Solo Track
            if track and track != self.song.master_track:
                track.solo = not track.solo  # Toggle du solo
                new_solo_state = 1 if track.solo else 0
                self.logger.debug(f"Solo piste (slot {slot}) basculé à {bool(new_solo_state)}")
            else:
                if not track:
                    self.logger.warning(f"Aucune piste trouvée pour le slot {slot} (type {param_type})")
                elif track == self.song.master_track:
                    self.logger.warning(f"Impossible de mettre en solo la piste Master via le slot {slot}")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 9:  # Chain Mute (Toggle)
            if chain:
                chain.mute = not chain.mute  # Toggle du mute de la chaîne
                new_mute_state = 1 if chain.mute else 0
                chain_path = self._get_chain_path(chain)
                self.osc_server.send("/live/chain/learning/mute", (-2, chain_path, new_mute_state))
                self.logger.debug(f"Mute chaîne (slot {slot}) basculé à {bool(new_mute_state)}")
            else:
                self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        elif param_type == 10:  # Chain Solo (Toggle)
            if chain:
                chain.solo = not chain.solo  # Toggle du solo de la chaîne
                new_solo_state = 1 if chain.solo else 0
                chain_path = self._get_chain_path(chain)
                self.osc_server.send("/live/chain/learning/solo", (-2, chain_path, new_solo_state))
                self.logger.debug(f"Solo chaîne (slot {slot}) basculé à {bool(new_solo_state)}")
            else:
                self.logger.warning(f"Aucune chaîne trouvée pour le slot {slot} (type {param_type})")
                self.osc_server.send("/live/learn/slot/cleared", (slot,))
        else:
            self.logger.error(f"Type de paramètre inconnu: {param_type}")

    def stop_learn_listen(self):
        self.logger.info("Stopping learn listen")
        for learn_slot in range(32):
            track = self.learn_slots[learn_slot].get("track")
            device = self.learn_slots[learn_slot].get("device")
            chain = self.learn_slots[learn_slot].get("chain")
            param_type = self.learn_slots[learn_slot].get("param_type")
            track_name = f"track{learn_slot}"

            if track and param_type:
                try:
                    if param_type == 1:  # Volume
                        if (track_name, "volume") in self.learn_listeners:
                            track.mixer_device.volume.remove_value_listener(self.learn_listeners[(track_name, "volume")])
                            del self.learn_listeners[(track_name, "volume")]

                    elif param_type == 2:  # Pan
                        if (track_name, "panning") in self.learn_listeners:
                            track.mixer_device.panning.remove_value_listener(self.learn_listeners[(track_name, "panning")])
                            del self.learn_listeners[(track_name, "panning")]

                    elif param_type == 3:  # Send
                        send_index = self.learn_slots[learn_slot].get("send_index")
                        if send_index is not None and (track_name, f"send_{send_index}") in self.learn_listeners:
                            track.mixer_device.sends[send_index].remove_value_listener(
                                self.learn_listeners[(track_name, f"send_{send_index}")])
                            del self.learn_listeners[(track_name, f"send_{send_index}")]

                    elif param_type == 4:  # Device Parameter
                        if device:
                            for key in list(self.learn_listeners.keys()):
                                if key.startswith(f"learn_{track_name}_device_param_"):
                                    try:
                                        param_index = int(key.split("_")[-1])
                                        if param_index < len(device.parameters):
                                            device.parameters[param_index].remove_value_listener(
                                                self.learn_listeners[key])
                                            del self.learn_listeners[key]
                                    except Exception as e:
                                        self.logger.error(f"Error removing device parameter listener: {e}")

                    elif param_type == 5:  # Chain Volume
                        if chain:
                            if (track_name, "chain_volume") in self.learn_listeners:
                                chain.mixer_device.volume.remove_value_listener(
                                    self.learn_listeners[(track_name, "chain_volume")])
                                del self.learn_listeners[(track_name, "chain_volume")]

                            # Supprimer les callbacks de la chaîne
                            callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                            if callbacks:
                                chain.remove_name_listener(callbacks["chain_name"])
                                track.remove_name_listener(callbacks["track_name"])
                                track.remove_color_listener(callbacks["track_color"])
                                chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                                del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

                    elif param_type == 7:  # Mute
                        listener_key = f"track_{learn_slot}_mute"
                        if listener_key in self.learn_listeners:
                            track.remove_mute_listener(self.learn_listeners[listener_key])
                            del self.learn_listeners[listener_key]

                    elif param_type == 8:  # Solo
                        listener_key = f"track_{learn_slot}_solo"
                        if listener_key in self.learn_listeners:
                            track.remove_solo_listener(self.learn_listeners[listener_key])
                            del self.learn_listeners[listener_key]

                    elif param_type == 9:  # Chain Mute
                        if chain:
                            if (track_name, "chain_mute") in self.learn_listeners:
                                chain.remove_mute_listener(
                                    self.learn_listeners[(track_name, "chain_mute")])
                                del self.learn_listeners[(track_name, "chain_mute")]

                            # Supprimer les callbacks de la chaîne
                            callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                            if callbacks:
                                chain.remove_name_listener(callbacks["chain_name"])
                                track.remove_name_listener(callbacks["track_name"])
                                track.remove_color_listener(callbacks["track_color"])
                                chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                                del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

                    elif param_type == 10:  # Chain Solo
                        if chain:
                            if (track_name, "chain_solo") in self.learn_listeners:
                                chain.remove_solo_listener(
                                    self.learn_listeners[(track_name, "chain_solo")])
                                del self.learn_listeners[(track_name, "chain_solo")]

                            # Supprimer les callbacks de la chaîne
                            callbacks = self.learn_listeners.get(f"learn_{track_name}_chain_callbacks", {})
                            if callbacks:
                                chain.remove_name_listener(callbacks["chain_name"])
                                track.remove_name_listener(callbacks["track_name"])
                                track.remove_color_listener(callbacks["track_color"])
                                chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                                del self.learn_listeners[f"learn_{track_name}_chain_callbacks"]

                except Exception as e:
                    self.logger.error(f"Erreur lors de la suppression des listeners pour le slot {learn_slot}: {e}")

            # Supprimer les listeners communs (couleur et nom de la piste)
            if track:
                try:
                    color_key = f"learn_{track_name}_color"
                    if color_key in self.learn_listeners:
                        track.remove_color_listener(self.learn_listeners[color_key])
                        del self.learn_listeners[color_key]

                    name_key = f"learn_{track_name}_name"
                    if name_key in self.learn_listeners:
                        track.remove_name_listener(self.learn_listeners[name_key])
                        del self.learn_listeners[name_key]

                except Exception as e:
                    self.logger.error(f"Erreur lors de la suppression des listeners de piste pour le slot {learn_slot}: {e}")

            self.learn_slots[learn_slot] = {
                "track": None,
                "device": None,                 "chain": None,
                "param_type": None,
                "param_index": None
            }

        self.logger.info("Tous les listeners de learning ont été supprimés et les slots réinitialisés")

    def _normalize_name(self, name):
        """Normalise un nom en retirant les accents et caractères spéciaux"""
        import unicodedata
        return ''.join(c for c in unicodedata.normalize('NFD', str(name))
                    if not unicodedata.combining(c))

    def send_learn_slot_reference(self, learn_slot):
        """Envoie les références (nom du device, nom de la track, couleur) pour un slot d'apprentissage"""
        slot_info = self.learn_slots.get(learn_slot)
        if not slot_info:
            return

        track = slot_info.get("track")
        if not track:
            return

        param_type = slot_info.get("param_type")

        if param_type in [5, 6, 9, 10]:  # Traiter le volume, le pan, le mute et le solo de chaîne
            chain = slot_info.get("chain")
            if not chain:
                return

            current_track = self._find_device_or_chain_track(chain)
            if not current_track:
                return

            device_name = self._normalize_name(chain.name)  # Normalisation du nom de la chaîne
            track_name = current_track.name
            track_color = current_track.color
        elif param_type == 4:
            # Cas spécial pour les device parameters
            device = slot_info.get("device")
            if not device:
                return

            # Trouver la track actuelle du device
            current_track = self._find_device_or_chain_track(device)
            if not current_track:
                return

            device_name = device.name
            track_name = current_track.name
            track_color = current_track.color
        else:
            # Cas pour volume, pan, sends (1, 2, 3)
            device_name = ""  # Device name vide pour ces cas
            track_name = track.name
            track_color = track.color

        self.osc_server.send("/live/learnslot/get/properties",
                            (learn_slot, device_name, track_name, track_color))

    def verify_learn_slots_validity(self):
        """Vérifie la validité de tous les slots après un changement dans les pistes"""
        all_tracks = [track for track in self.song.tracks] + \
                    list(self.song.return_tracks) + \
                    [self.song.master_track]

        for slot_index, slot_data in self.learn_slots.items():
            track = slot_data.get("track")
            device = slot_data.get("device")
            chain = slot_data.get("chain")
            param_type = slot_data.get("param_type")

            # On ne traite que les slots qui ont des données
            if track is None or param_type is None:
                continue

            if param_type == 4 and device is not None:
                # Pour le type 4 (device parameter)
                device_found = False
                current_track = self._find_device_or_chain_track(device)

                if current_track:
                    device_found = True
                    # Si le device existe mais a changé de piste
                    if track != current_track:
                        track_name = f"track{slot_index}"
                        self._update_device_track_listeners(slot_index, track_name, device, current_track)

                if not device_found:
                    self.logger.info(f"Device removed - clearing slot {slot_index}")
                    # Utiliser delete_single_slot pour nettoyer correctement tous les listeners
                    self.delete_single_slot(slot_index)

            elif param_type in [5, 6, 9, 10] and chain is not None:
                # Pour les types 5 et 6 (chain volume et chain pan)
                chain_found = False
                current_track = self._find_device_or_chain_track(chain)

                if current_track:
                    chain_found = True
                    # Si la chaîne existe mais a changé de piste
                    if track != current_track:
                        track_name = f"track{slot_index}"
                        self._update_device_track_listeners(slot_index, track_name, chain, current_track)

                if not chain_found:
                    self.logger.info(f"Chain removed - clearing slot {slot_index}")
                    # Utiliser delete_single_slot pour nettoyer correctement tous les listeners
                    self.delete_single_slot(slot_index)

            else:  # Pour les types 1, 2, 3
                # Vérifier si la piste existe toujours
                if track not in all_tracks:
                    self.logger.info(f"Track removed - clearing slot {slot_index}")
                    # Utiliser delete_single_slot pour nettoyer correctement tous les listeners
                    self.delete_single_slot(slot_index)

    def _find_device_or_chain_track(self, element):
        """Trouve la piste qui contient le device ou la chaîne spécifié"""
        def search_in_devices(devices):
            for d in devices:
                if d == element:
                    return True
                # Vérifier si c'est un rack (instrument, audio, ou midi)
                if hasattr(d, 'chains'):
                    for chain in d.chains:
                        if chain == element:  # Vérifie si c'est la chaîne recherchée
                            return True
                        # Recherche récursive dans les devices de la chaîne
                        if search_in_devices(chain.devices):
                            return True
            return False

        all_tracks = list(self.song.tracks) + list(self.song.return_tracks) + [self.song.master_track]
        for track in all_tracks:
            if search_in_devices(track.devices):
                return track
        return None


    def delete_single_slot(self, learn_slot):
        """Arrête les listeners pour un slot spécifique"""
        # Convertir learn_slot en entier s'il est passé comme une liste ou un float
        if isinstance(learn_slot, list):
            learn_slot = learn_slot[0]

        try:
            learn_slot = int(learn_slot)
        except (ValueError, TypeError):
            self.logger.error(f"Invalid learn slot format: {learn_slot}")
            return

        # Vérifier que le slot est valide
        if learn_slot not in self.learn_slots:
            self.logger.error(f"Invalid learn slot: {learn_slot}")
            return

        track = self.learn_slots[learn_slot].get("track")
        device = self.learn_slots[learn_slot].get("device")
        chain = self.learn_slots[learn_slot].get("chain")
        param_type = self.learn_slots[learn_slot].get("param_type")
        track_name = f"track{learn_slot}"

        if track and param_type:
            try:
                if param_type in [5, 6, 9, 10]:  # Chain Volume, Chain Pan, Chain Mute, Chain Solo
                    if chain:
                        listener_type = ""
                        if param_type == 5:
                            listener_type = "chain_volume"
                            chain.mixer_device.volume.remove_value_listener(self.learn_listeners[(track_name, listener_type)])
                        elif param_type == 6:
                            listener_type = "chain_panning"
                            chain.mixer_device.panning.remove_value_listener(self.learn_listeners[(track_name, listener_type)])
                        elif param_type == 9:
                            listener_type = "chain_mute"
                            chain.remove_mute_listener(self.learn_listeners[(track_name, listener_type)])
                        elif param_type == 10:
                            listener_type = "chain_solo"
                            chain.remove_solo_listener(self.learn_listeners[(track_name, listener_type)])

                        if (track_name, listener_type) in self.learn_listeners:
                            del self.learn_listeners[(track_name, listener_type)]

                        # Supprimer les callbacks de la chaîne
                        callback_key = f"learn_{track_name}_chain_callbacks"
                        callbacks = self.learn_listeners.get(callback_key, {})
                        if callbacks:
                            try:
                                chain.remove_name_listener(callbacks["chain_name"])
                                track.remove_name_listener(callbacks["track_name"])
                                track.remove_color_listener(callbacks["track_color"])
                                chain.canonical_parent.remove_chains_listener(callbacks["chains_changed"])
                                del self.learn_listeners[callback_key]
                                self.logger.debug(f"Chain context listeners supprimés pour le slot {learn_slot} (chain: {chain.name})")
                            except Exception as e:
                                self.logger.error(f"Erreur lors de la suppression des chain context listeners pour le slot {learn_slot}: {e}")

                # Supprimer les listeners spécifiques aux paramètres
                if param_type == 1:  # Volume
                    if (track_name, "volume") in self.learn_listeners:
                        track.mixer_device.volume.remove_value_listener(self.learn_listeners[(track_name, "volume")])
                        del self.learn_listeners[(track_name, "volume")]

                elif param_type == 2:  # Pan
                    if (track_name, "panning") in self.learn_listeners:
                        track.mixer_device.panning.remove_value_listener(self.learn_listeners[(track_name, "panning")])
                        del self.learn_listeners[(track_name, "panning")]

                elif param_type == 3:  # Send
                    send_index = self.learn_slots[learn_slot].get("send_index")
                    if send_index is not None and (track_name, f"send_{send_index}") in self.learn_listeners:
                        track.mixer_device.sends[send_index].remove_value_listener(
                            self.learn_listeners[(track_name, f"send_{send_index}")])
                        del self.learn_listeners[(track_name, f"send_{send_index}")]

                elif param_type == 4:  # Device Parameter
                    if device:
                        # Supprimer le listener du paramètre spécifique
                        param_index = self.learn_slots[learn_slot].get("param_index")
                        listener_key = f"learn_{track_name}_device_param_{param_index}"
                        if listener_key in self.learn_listeners:
                            try:
                                device.parameters[param_index].remove_value_listener(
                                    self.learn_listeners[listener_key])
                                del self.learn_listeners[listener_key]
                            except Exception as e:
                                self.logger.error(f"Error removing device parameter listener: {e}")

                        # Supprimer les callbacks du device (nom du device, nom de piste, couleur)
                        callback_key = f"learn_{track_name}_device_callbacks"
                        callbacks = self.learn_listeners.get(callback_key, {})
                        if callbacks:
                            try:
                                device.remove_name_listener(callbacks["device_name"])
                                track.remove_name_listener(callbacks["track_name"])
                                track.remove_color_listener(callbacks["track_color"])
                                del self.learn_listeners[callback_key]
                                self.logger.debug(f"Device context listeners supprimés pour le slot {learn_slot} (device: {device.name})")
                            except Exception as e:
                                self.logger.error(f"Erreur lors de la suppression des device context listeners pour le slot {learn_slot}: {e}")

            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression des listeners pour le slot {learn_slot}: {e}")

        # Supprimer les listeners communs
        if track:
            try:
                # Supprimer le listener de couleur
                color_key = f"learn_{track_name}_color"
                if color_key in self.learn_listeners:
                    track.remove_color_listener(self.learn_listeners[color_key])
                    del self.learn_listeners[color_key]
                    self.logger.debug(f"Track color listener supprimé pour le slot {learn_slot} (track: {track.name})")

                # Supprimer le listener de nom
                name_key = f"learn_{track_name}_name"
                if name_key in self.learn_listeners:
                    track.remove_name_listener(self.learn_listeners[name_key])
                    del self.learn_listeners[name_key]
                    self.logger.debug(f"Track name listener supprimé pour le slot {learn_slot} (track: {track.name})")

            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression des listeners de piste pour le slot {learn_slot}: {e}")

        # Réinitialiser le slot
        param_index = self.learn_slots[learn_slot].get("param_index") if param_type == 4 else None
        self.learn_slots[learn_slot] = {
            "track": None,
            "device": None,
            "chain": None,
            "param_type": None,
            "param_index": param_index
        }

        self.osc_server.send("/live/learn/slot/cleared", (learn_slot,))

        # Log du nombre total de listeners restants
        self.logger.info(f"Slot {learn_slot} supprimé. Nombre total de listeners restants: {len(self.learn_listeners)}")

    def _setup_mute_listener(self, learn_slot, track_name, learned_track):
        """Configure un listener pour le mute"""
        def callback():
            if not hasattr(callback, 'is_active') or callback.is_active:
                param_id = f"learn_{track_name}_mute"
                value = int(learned_track.mute)

                def send_value(val):
                    # Correction du chemin OSC
                    self.osc_server.send("/live/tracklearn/get/mute", (learn_slot, val))
                    self.logger.info(f"Mute changed for {track_name}: {val}")

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        callback.is_active = True
        callback.track = learned_track

        listener_key = f"track_{learn_slot}_mute"
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                learned_track.remove_mute_listener(old_callback)
            except:
                self.logger.debug(f"Impossible de supprimer l'ancien listener pour {listener_key}")

        self.learn_listeners[listener_key] = callback
        learned_track.add_mute_listener(callback)

    def _setup_solo_listener(self, learn_slot, track_name, learned_track):
        """Configure un listener pour le solo"""
        def callback():
            if not hasattr(callback, 'is_active') or callback.is_active:
                param_id = f"learn_{track_name}_solo"
                value = int(learned_track.solo)

                def send_value(val):
                    # Correction du chemin OSC
                    self.osc_server.send("/live/tracklearn/get/solo", (learn_slot, val))
                    self.logger.info(f"Solo changed for {track_name}: {val}")

                self.parameter_throttler.update_parameter(param_id, value, send_value)

        callback.is_active = True
        callback.track = learned_track

        listener_key = f"track_{learn_slot}_solo"
        if listener_key in self.learn_listeners:
            old_callback = self.learn_listeners[listener_key]
            old_callback.is_active = False
            try:
                learned_track.remove_solo_listener(old_callback)
            except:
                self.logger.debug(f"Impossible de supprimer l'ancien listener pour {listener_key}")

        self.learn_listeners[listener_key] = callback
        learned_track.add_solo_listener(callback)

    def _setup_chain_mute_listener(self, learn_slot: int, track_name: str, chain) -> None:
        """Configure le listener pour le mute de la chaîne"""
        def parameter_changed_callback():
            value = 1 if chain.mute else 0
            chain_path = self._get_chain_path(chain)

            # Message de debug pour tracer l'exécution
            self.logger.debug(f"Chain {chain_path} mute changed to: {value}")

            # Envoi direct sans throttler pour les valeurs binaires
            # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
            self.osc_server.send("/live/chainlearn/get/mute", (learn_slot, value))

        listener_key = (track_name, "chain_mute")

        # Supprimer l'ancien listener s'il existe
        if listener_key in self.learn_listeners:
            try:
                chain.remove_mute_listener(self.learn_listeners[listener_key])
            except:
                pass

        # Ajouter le nouveau listener
        self.learn_listeners[listener_key] = parameter_changed_callback
        chain.add_mute_listener(parameter_changed_callback)

        # Appel initial pour envoyer l'état actuel
        parameter_changed_callback()

    def _setup_chain_solo_listener(self, learn_slot: int, track_name: str, chain) -> None:
        """Configure le listener pour le solo de la chaîne"""
        def parameter_changed_callback():
            value = 1 if chain.solo else 0
            chain_path = self._get_chain_path(chain)

            # Message de debug pour tracer l'exécution
            self.logger.debug(f"Chain {chain_path} solo changed to: {value}")

            # Envoi direct sans throttler pour les valeurs binaires
            # Envoyer uniquement l'index du slot et la valeur, pas le chemin de la chaîne
            self.osc_server.send("/live/chainlearn/get/solo", (learn_slot, value))

        listener_key = (track_name, "chain_solo")

        # Supprimer l'ancien listener s'il existe
        if listener_key in self.learn_listeners:
            try:
                chain.remove_solo_listener(self.learn_listeners[listener_key])
            except:
                pass

        # Ajouter le nouveau listener
        self.learn_listeners[listener_key] = parameter_changed_callback
        chain.add_solo_listener(parameter_changed_callback)

        # Appel initial pour envoyer l'état actuel
        parameter_changed_callback()
