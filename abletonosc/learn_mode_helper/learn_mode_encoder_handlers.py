"""
Handlers pour les encoders du mode learn
Gère les ajustements des paramètres assignés aux slots du mode learn via les encoders
"""

import json
import traceback


def adjust_learn_parameter(self, params):
    """
    Ajuste un paramètre assigné à un slot du mode learn

    LOGIQUE DU MODE LEARN:
    - 32 slots (0-31) organisés en 4 pages de 8 slots
    - 8 encoders correspondent aux 8 slots de la page courante
    - Encodeur 0 → Slot 0 de la page courante
    - Encodeur 1 → Slot 1 de la page courante, etc.
    - Chaque slot peut contenir n'importe quel type de paramètre

    params: [encoder_index, delta_value]
    encoder_index: 0-7 (index de l'encodeur)
    delta_value: changement à appliquer
    """
    try:
        if not params or len(params) != 2:
            self.logger.warning(f"Format de paramètres incorrect pour adjust_learn_parameter: {params}")
            return

        encoder_index = int(params[0]) - 1  # Go envoie 1-8, on veut 0-7
        delta_value = float(params[1])

        self.logger.debug(f"adjust_learn_parameter: encoder={encoder_index}, delta={delta_value}")

        # Vérifier que l'index de l'encodeur est valide (0-7)
        if encoder_index < 0 or encoder_index >= 8:
            self.logger.warning(f"Index d'encodeur invalide: {encoder_index}")
            return

        # Calculer l'index du slot basé sur la page courante
        # Note: On n'a pas accès direct à la page courante depuis Python
        # On va utiliser l'encodeur directement comme index de slot relatif (0-7)
        slot_index = encoder_index

        self.logger.debug(f"Slot calculé: {slot_index}")

        # Récupérer les informations du slot
        if slot_index not in self.learn_slots:
            self.logger.debug(f"Slot {slot_index} n'existe pas dans learn_slots")
            return

        slot_data = self.learn_slots[slot_index]
        param_type = slot_data.get("param_type")

        if param_type is None:
            self.logger.debug(f"Slot {slot_index} n'a pas de paramètre assigné")
            return

        self.logger.debug(f"Slot {slot_index} contient un paramètre de type {param_type}")

        # Traiter selon le type de paramètre assigné au slot
        if param_type == 1:  # Volume de piste
            self._adjust_track_volume(slot_data, delta_value)
        elif param_type == 2:  # Panoramique de piste
            self._adjust_track_pan(slot_data, delta_value)
        elif param_type == 3:  # Send de piste
            self._adjust_track_send(slot_data, delta_value)
        elif param_type == 4:  # Paramètre de device
            self._adjust_device_parameter(slot_data, delta_value)
        elif param_type == 5:  # Volume de chaîne
            self._adjust_chain_volume(slot_data, delta_value)
        elif param_type == 6:  # Panoramique de chaîne
            self._adjust_chain_pan(slot_data, delta_value)
        elif param_type == 7:  # Mute de piste
            self._adjust_track_mute(slot_data, delta_value)
        elif param_type == 8:  # Solo de piste
            self._adjust_track_solo(slot_data, delta_value)
        elif param_type == 9:  # Mute de chaîne
            self._adjust_chain_mute(slot_data, delta_value)
        elif param_type == 10:  # Solo de chaîne
            self._adjust_chain_solo(slot_data, delta_value)
        else:
            self.logger.warning(f"Type de paramètre non supporté: {param_type}")

    except Exception as e:
        self.logger.error(f"Erreur lors de l'ajustement du paramètre learn: {e}")
        self.logger.error(traceback.format_exc())


def _adjust_track_volume(self, slot_data, delta_value):
    """Ajuste le volume d'une piste"""
    try:
        track = slot_data.get("track")
        if not track or not hasattr(track, 'mixer_device'):
            self.logger.warning("Piste invalide pour ajustement volume")
            return

        volume_param = track.mixer_device.volume
        current_value = volume_param.value

        # Scaling similaire au mode volume
        scaled_delta = delta_value / 5.0
        new_value = max(volume_param.min, min(volume_param.max, current_value + scaled_delta))

        volume_param.value = new_value
        self.logger.debug(f"Volume piste ajusté: {current_value:.3f} -> {new_value:.3f}")

    except Exception as e:
        self.logger.error(f"Erreur ajustement volume piste: {e}")


def _adjust_track_pan(self, slot_data, delta_value):
    """Ajuste le panoramique d'une piste"""
    try:
        track = slot_data.get("track")
        if not track or not hasattr(track, 'mixer_device'):
            self.logger.warning("Piste invalide pour ajustement pan")
            return

        pan_param = track.mixer_device.panning
        current_value = pan_param.value

        # Scaling similaire au mode volume
        scaled_delta = delta_value / 5.0
        new_value = max(pan_param.min, min(pan_param.max, current_value + scaled_delta))

        pan_param.value = new_value
        self.logger.debug(f"Pan piste ajusté: {current_value:.3f} -> {new_value:.3f}")

    except Exception as e:
        self.logger.error(f"Erreur ajustement pan piste: {e}")


def _adjust_track_send(self, slot_data, delta_value):
    """Ajuste un send d'une piste"""
    try:
        track = slot_data.get("track")
        send_index = slot_data.get("param_index")

        if not track or send_index is None:
            self.logger.warning("Données send invalides")
            return

        if send_index >= len(track.mixer_device.sends):
            self.logger.warning(f"Index send invalide: {send_index}")
            return

        send_param = track.mixer_device.sends[send_index]
        current_value = send_param.value

        # Scaling similaire au mode volume
        scaled_delta = delta_value / 5.0
        new_value = max(send_param.min, min(send_param.max, current_value + scaled_delta))

        send_param.value = new_value
        self.logger.debug(f"Send {send_index} piste ajusté: {current_value:.3f} -> {new_value:.3f}")

    except Exception as e:
        self.logger.error(f"Erreur ajustement send piste: {e}")


def _adjust_device_parameter(self, slot_data, delta_value):
    """Ajuste un paramètre de device"""
    try:
        device = slot_data.get("device")
        param_index = slot_data.get("param_index")

        if not device or param_index is None:
            self.logger.warning("Données device invalides")
            return

        if param_index >= len(device.parameters):
            self.logger.warning(f"Index paramètre device invalide: {param_index}")
            return

        parameter = device.parameters[param_index]
        current_value = parameter.value

        # Déterminer si c'est un rack et si le paramètre est quantifié
        is_rack = _is_device_rack(device)
        is_quantized = _is_parameter_quantized(parameter, is_rack)

        if is_quantized:
            # Utiliser le système de buffers pour les paramètres quantifiés
            param_id = f"device_{id(device)}_{param_index}"
            self._adjust_quantized_parameter(parameter, param_id, delta_value)
        else:
            # Paramètres continus avec scaling adaptatif
            if is_rack:
                scaled_delta = delta_value * 25.4  # Racks (0-127)
            else:
                scaled_delta = delta_value / 5.0   # Devices normaux (0-1)

            new_value = max(parameter.min, min(parameter.max, current_value + scaled_delta))
            parameter.value = new_value
            self.logger.debug(f"Paramètre device ajusté: {current_value:.3f} -> {new_value:.3f}")

    except Exception as e:
        self.logger.error(f"Erreur ajustement paramètre device: {e}")


def _adjust_chain_volume(self, slot_data, delta_value):
    """Ajuste le volume d'une chaîne"""
    try:
        # TODO: Implémenter selon la structure des chaînes dans slot_data
        self.logger.debug("Ajustement volume chaîne - à implémenter")
    except Exception as e:
        self.logger.error(f"Erreur ajustement volume chaîne: {e}")


def _adjust_chain_pan(self, slot_data, delta_value):
    """Ajuste le panoramique d'une chaîne"""
    try:
        # TODO: Implémenter selon la structure des chaînes dans slot_data
        self.logger.debug("Ajustement pan chaîne - à implémenter")
    except Exception as e:
        self.logger.error(f"Erreur ajustement pan chaîne: {e}")


def _adjust_track_mute(self, slot_data, delta_value):
    """Toggle le mute d'une piste"""
    try:
        track = slot_data.get("track")
        if not track:
            self.logger.warning("Piste invalide pour toggle mute")
            return

        # Pour les paramètres booléens, utiliser un seuil pour déclencher le toggle
        if abs(delta_value) >= 0.1:  # Seuil pour éviter les toggles accidentels
            track.mute = not track.mute
            self.logger.debug(f"Mute piste togglé: {track.mute}")

    except Exception as e:
        self.logger.error(f"Erreur toggle mute piste: {e}")


def _adjust_track_solo(self, slot_data, delta_value):
    """Toggle le solo d'une piste"""
    try:
        track = slot_data.get("track")
        if not track:
            self.logger.warning("Piste invalide pour toggle solo")
            return

        # Pour les paramètres booléens, utiliser un seuil pour déclencher le toggle
        if abs(delta_value) >= 0.1:  # Seuil pour éviter les toggles accidentels
            track.solo = not track.solo
            self.logger.debug(f"Solo piste togglé: {track.solo}")

    except Exception as e:
        self.logger.error(f"Erreur toggle solo piste: {e}")


def _adjust_chain_mute(self, slot_data, delta_value):
    """Toggle le mute d'une chaîne"""
    try:
        # TODO: Implémenter selon la structure des chaînes dans slot_data
        self.logger.debug("Toggle mute chaîne - à implémenter")
    except Exception as e:
        self.logger.error(f"Erreur toggle mute chaîne: {e}")


def _adjust_chain_solo(self, slot_data, delta_value):
    """Toggle le solo d'une chaîne"""
    try:
        # TODO: Implémenter selon la structure des chaînes dans slot_data
        self.logger.debug("Toggle solo chaîne - à implémenter")
    except Exception as e:
        self.logger.error(f"Erreur toggle solo chaîne: {e}")


def _adjust_quantized_parameter(self, parameter, param_id, delta_value):
    """Ajuste un paramètre quantifié en utilisant le système de buffers"""
    try:
        current_value = parameter.value

        # Récupérer le buffer actuel pour ce paramètre
        current_buffer = self.quantized_param_buffers.get(param_id, 0.0)

        # Ajouter le delta au buffer
        current_buffer += delta_value

        # Seuil pour déclencher un changement
        threshold = 0.5

        if abs(current_buffer) >= threshold:
            # Déterminer la direction du changement
            direction = 1 if current_buffer > 0 else -1

            # Pour les paramètres quantifiés, incrémenter/décrémenter par pas discrets
            if hasattr(parameter, 'value_items') and parameter.value_items:
                # Utiliser les éléments de valeur disponibles
                num_steps = len(parameter.value_items)
                step_size = (parameter.max - parameter.min) / (num_steps - 1) if num_steps > 1 else 0
            else:
                # Estimation basée sur la plage
                value_range = parameter.max - parameter.min
                if abs(value_range - 127) < 0.1:  # Rack parameter
                    step_size = 1.0
                else:
                    num_steps = max(2, int(value_range * 10))
                    step_size = value_range / (num_steps - 1) if num_steps > 1 else value_range

            potential_new_value = current_value + (direction * step_size)
            potential_new_value = max(parameter.min, min(parameter.max, potential_new_value))

            # Vérifier si la nouvelle valeur est différente
            if abs(potential_new_value - current_value) > 0.001:
                parameter.value = potential_new_value
                # Réduire le buffer du montant utilisé
                self.quantized_param_buffers[param_id] = current_buffer - (direction * threshold)
                self.logger.debug(f"Paramètre quantifié ajusté: {current_value:.3f} -> {potential_new_value:.3f}")
            else:
                # Aux extrémités, vider le buffer
                self.quantized_param_buffers[param_id] = 0.0
        else:
            # Pas assez d'accumulation, stocker le buffer
            self.quantized_param_buffers[param_id] = current_buffer

    except Exception as e:
        self.logger.error(f"Erreur ajustement paramètre quantifié: {e}")


def _is_device_rack(device):
    """Détermine si un device est un rack"""
    try:
        return (hasattr(device, 'can_have_chains') and device.can_have_chains) or \
               (hasattr(device, 'can_have_drum_pads') and device.can_have_drum_pads)
    except:
        return False


def _is_parameter_quantized(parameter, is_rack):
    """Détermine si un paramètre est quantifié"""
    try:
        # Vérifier s'il y a des éléments de valeur définis (liste de choix)
        if hasattr(parameter, 'value_items') and parameter.value_items:
            return True

        # Vérifier si c'est un paramètre booléen
        if hasattr(parameter, 'is_enabled') or parameter.name.lower() in ['on', 'off', 'enable', 'bypass']:
            return True

        # Pour les racks, vérifier la plage 0-127
        if is_rack:
            value_range = parameter.max - parameter.min
            if abs(value_range - 127) < 0.1:
                return True

        # Paramètres avec une petite plage de valeurs entières
        value_range = parameter.max - parameter.min
        if value_range <= 10 and value_range == int(value_range):
            return True

        return False

    except:
        return False
